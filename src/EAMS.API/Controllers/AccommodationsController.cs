using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Identity.Web.Resource;
using EAMS.API.DTOs;
using EAMS.API.Services;
using EAMS.Domain.Interfaces;
using EAMS.Domain.Entities;
using EAMS.Domain.Entities.Enums;
using EAMS.Domain.Exceptions;
using AutoMapper;
using NetTopologySuite.Geometries;

namespace EAMS.API.Controllers;

[Authorize]
[ApiController]
[Route("api/[controller]")]
public class AccommodationsController : ControllerBase
{
    private readonly IAccommodationService _accommodationService;
    private readonly ILogger<AccommodationsController> _logger;
    private readonly IMapper _mapper;
    private readonly EnumService _enumService;

    public AccommodationsController(
        IAccommodationService accommodationService,
        ILogger<AccommodationsController> logger,
        IMapper mapper,
        EnumService enumService)
    {
        _accommodationService = accommodationService;
        _logger = logger;
        _mapper = mapper;
        _enumService = enumService;
    }

    /// <summary>
    /// Get all accommodations with optional filtering
    /// </summary>
    [HttpGet]
    [Authorize(Roles = "Users, Managers")]
    public async Task<ActionResult<AccommodationsResponseDto>> GetAccommodations()
    {
        var accommodations = await _accommodationService.GetAll();
        var accommodationDtos = _mapper.Map<IEnumerable<AccommodationDto>>(accommodations);

        // Check if current user can create accommodations (Managers role)
        var canCreateAccommodation = User.IsInRole("Managers");

        var response = new AccommodationsResponseDto
        {
            Accommodations = accommodationDtos,
            CanCreateAccommodation = canCreateAccommodation
        };

        return Ok(response);
    }

    /// <summary>
    /// Get accommodation by ID
    /// </summary>
    [HttpGet("{id}")]
    [Authorize(Roles = "Users, Managers")]
    public async Task<ActionResult<AccommodationDto>> GetAccommodation(Int64 id)
    {
        var accommodation = await _accommodationService.GetById(id);

        if (accommodation == null)
        {
            throw new EntityNotFoundException("Accommodation", id);
        }

        var response = _mapper.Map<AccommodationDto>(accommodation);
        return Ok(response);
    }

    /// <summary>
    /// Get form data for creating a new accommodation
    /// </summary>
    [HttpGet("new")]
    [Authorize(Roles = "Managers")]
    public ActionResult<FormResponseDto<AccommodationDto>> GetNewAccommodationForm()
    {
        var formMetadata = _enumService.GetFormMetadata();
        var permissions = new PermissionsDto
        {
            CanCreateAccommodation = User.IsInRole("Managers")
        };

        var response = new FormResponseDto<AccommodationDto>
        {
            Data = new FormDataDto<AccommodationDto>
            {
                Model = new AccommodationDto(), // Empty model for new form
                FormMetadata = formMetadata,
                Permissions = permissions
            }
        };

        return Ok(response);
    }

    /// <summary>
    /// Get form data for editing an existing accommodation
    /// </summary>
    [HttpGet("edit/{id}")]
    [Authorize(Roles = "Users, Managers")]
    public async Task<ActionResult<FormResponseDto<AccommodationDto>>> GetEditAccommodationForm(Int64 id)
    {
        var accommodation = await _accommodationService.GetById(id);

        if (accommodation == null)
        {
            throw new EntityNotFoundException("Accommodation", id);
        }

        var accommodationDto = _mapper.Map<AccommodationDto>(accommodation);
        var formMetadata = _enumService.GetFormMetadata();
        var isManager = User.IsInRole("Managers");

        var permissions = new PermissionsDto
        {
            CanEditAccommodation = isManager,
            CanDeleteAccommodation = isManager
        };

        var response = new FormResponseDto<AccommodationDto>
        {
            Data = new FormDataDto<AccommodationDto>
            {
                Model = accommodationDto,
                FormMetadata = formMetadata,
                Permissions = permissions
            }
        };

        return Ok(response);
    }

    /// <summary>
    /// Create a new accommodation
    /// </summary>
    [HttpPost]
    [Authorize(Roles = "Managers")]
    public async Task<ActionResult<AccommodationDto>> CreateAccommodation(AccommodationDto accommodationDto)
    {
        var accommodation = _mapper.Map<Accommodation>(accommodationDto);
        var createdAccommodation = await _accommodationService.Create(accommodation);
        var response = _mapper.Map<AccommodationDto>(createdAccommodation);

        return CreatedAtAction(nameof(GetAccommodation), new { id = createdAccommodation.Id }, response);
    }

    /// <summary>
    /// Update an existing accommodation
    /// </summary>
    [HttpPut("{id}")]
    [Authorize(Roles = "Managers")]
    public async Task<ActionResult<AccommodationDto>> UpdateAccommodation(Int64 id, AccommodationDto accommodationDto)
    {
        var accommodation = _mapper.Map<Accommodation>(accommodationDto);
        var updatedAccommodation = await _accommodationService.Update(accommodation);
        var response = _mapper.Map<AccommodationDto>(updatedAccommodation);

        return Ok(response);
    }

    /// <summary>
    /// Delete an accommodation
    /// </summary>
    [HttpDelete("{id}")]
    [Authorize(Roles = "Managers")]
    public async Task<ActionResult<bool>> DeleteAccommodation(Int64 id)
    {
        var result = await _accommodationService.Delete(id);
        return Ok(result);
    }
}
