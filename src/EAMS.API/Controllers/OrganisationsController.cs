using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Identity.Web.Resource;
using EAMS.API.DTOs;
using EAMS.API.Services;
using EAMS.Domain.Interfaces;
using EAMS.Domain.Entities;
using EAMS.Domain.Exceptions;
using AutoMapper;

namespace EAMS.API.Controllers;

[Authorize]
[ApiController]
[RequiredScope(RequiredScopesConfigurationKey = "AzureAd:Scopes:Organisations")]
[Route("api/[controller]")]
public class OrganisationsController : ControllerBase
{
    private readonly IOrganisationService _organisationService;
    private readonly ILogger<OrganisationsController> _logger;
    private readonly IMapper _mapper;
    private readonly EnumService _enumService;

    public OrganisationsController(
        IOrganisationService organisationService,
        ILogger<OrganisationsController> logger,
        IMapper mapper,
        EnumService enumService)
    {
        _organisationService = organisationService;
        _logger = logger;
        _mapper = mapper;
        _enumService = enumService;
    }

    /// <summary>
    /// Get all organisations
    /// </summary>
    [HttpGet]
    [Authorize(Roles = "Users, Managers")]
    public async Task<ActionResult<ListResponseDto<OrganisationDto>>> GetOrganisations()
    {
        var organisations = await _organisationService.GetAll();
        var organisationDtos = _mapper.Map<IEnumerable<OrganisationDto>>(organisations);

        var response = new ListResponseDto<OrganisationDto>
        {
            Data = organisationDtos,
            Permissions = new Dictionary<string, bool>
            {
                ["canCreateOrganisation"] = User.IsInRole("Managers")
            }
        };

        return Ok(response);
    }

    /// <summary>
    /// Get organisation by ID
    /// </summary>
    [HttpGet("{id}")]
    [Authorize(Roles = "Users, Managers")]
    public async Task<ActionResult<OrganisationDto>> GetOrganisation(Guid id)
    {
        var organisation = await _organisationService.GetById(id);

        if (organisation == null)
        {
            throw new EntityNotFoundException("Organisation", id);
        }

        var response = _mapper.Map<OrganisationDto>(organisation);
        return Ok(response);
    }

    /// <summary>
    /// Get form data for creating a new organisation
    /// </summary>
    [HttpGet("new")]
    [Authorize(Roles = "Managers")]
    public ActionResult<FormResponseDto<OrganisationDto>> GetNewOrganisationForm()
    {
        var formMetadata = _enumService.GetFormMetadata();
        var permissions = new PermissionsDto
        {
            Permissions = new Dictionary<string, bool>
            {
                ["canCreateOrganisation"] = User.IsInRole("Managers")
            }
        };

        var response = new FormResponseDto<OrganisationDto>
        {
            Data = new FormDataDto<OrganisationDto>
            {
                Model = new OrganisationDto(), // Empty model for new form
                FormMetadata = formMetadata,
                Permissions = permissions
            }
        };

        return Ok(response);
    }

    /// <summary>
    /// Get form data for editing an existing organisation
    /// </summary>
    [HttpGet("edit/{id}")]
    [Authorize(Roles = "Users, Managers")]
    public async Task<ActionResult<FormResponseDto<OrganisationDto>>> GetEditOrganisationForm(Guid id)
    {
        var organisation = await _organisationService.GetById(id);

        if (organisation == null)
        {
            throw new EntityNotFoundException("Organisation", id);
        }

        var organisationDto = _mapper.Map<OrganisationDto>(organisation);
        var formMetadata = _enumService.GetFormMetadata();
        var isManager = User.IsInRole("Managers");

        var permissions = new PermissionsDto
        {
            Permissions = new Dictionary<string, bool>
            {
                ["canEditOrganisation"] = isManager,
                ["canDeleteOrganisation"] = isManager
            }
        };

        var response = new FormResponseDto<OrganisationDto>
        {
            Data = new FormDataDto<OrganisationDto>
            {
                Model = organisationDto,
                FormMetadata = formMetadata,
                Permissions = permissions
            }
        };

        return Ok(response);
    }

    /// <summary>
    /// Create a new organisation
    /// </summary>
    [HttpPost]
    [Authorize(Roles = "Managers")]
    public async Task<ActionResult<OrganisationDto>> CreateOrganisation(OrganisationDto organisationDto)
    {
        var organisation = _mapper.Map<Organisation>(organisationDto);
        var createdOrganisation = await _organisationService.Create(organisation);
        var response = _mapper.Map<OrganisationDto>(createdOrganisation);

        return CreatedAtAction(nameof(GetOrganisation), new { id = createdOrganisation.Id }, response);
    }

    /// <summary>
    /// Update an existing organisation
    /// </summary>
    [HttpPut("{id}")]
    [Authorize(Roles = "Managers")]
    public async Task<ActionResult<OrganisationDto>> UpdateOrganisation(Guid id, OrganisationDto organisationDto)
    {
        var organisation = _mapper.Map<Organisation>(organisationDto);
        var updatedOrganisation = await _organisationService.Update(organisation);
        var response = _mapper.Map<OrganisationDto>(updatedOrganisation);

        return Ok(response);
    }

    /// <summary>
    /// Delete an organisation
    /// </summary>
    [HttpDelete("{id}")]
    [Authorize(Roles = "Managers")]
    public async Task<ActionResult<bool>> DeleteOrganisation(Guid id)
    {
        var result = await _organisationService.Delete(id);
        return Ok(result);
    }
}
