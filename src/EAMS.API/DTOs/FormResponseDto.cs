namespace EAMS.API.DTOs;

/// <summary>
/// Generic response wrapper for form data with model, metadata, and permissions
/// </summary>
/// <typeparam name="T">The type of the model data</typeparam>
public class FormResponseDto<T>
{
    /// <summary>
    /// The main data object
    /// </summary>
    public FormDataDto<T> Data { get; set; } = new();
}

/// <summary>
/// Container for form data including model, metadata, and permissions
/// </summary>
/// <typeparam name="T">The type of the model data</typeparam>
public class FormDataDto<T>
{
    /// <summary>
    /// The model data
    /// </summary>
    public T? Model { get; set; }

    /// <summary>
    /// Form metadata including enum options and other form-related data
    /// </summary>
    public FormMetadataDto FormMetadata { get; set; } = new();

    /// <summary>
    /// User permissions for this form/model
    /// </summary>
    public PermissionsDto Permissions { get; set; } = new();
}

/// <summary>
/// Form metadata containing enum options and other form-related data
/// </summary>
public class FormMetadataDto
{
    /// <summary>
    /// Accommodation type options
    /// </summary>
    public List<EnumOptionDto> AccommodationType { get; set; } = new();

    /// <summary>
    /// Density options
    /// </summary>
    public List<EnumOptionDto> Density { get; set; } = new();

    /// <summary>
    /// Region options
    /// </summary>
    public List<EnumOptionDto> Region { get; set; } = new();

    /// <summary>
    /// Duration options
    /// </summary>
    public List<EnumOptionDto> Duration { get; set; } = new();

    /// <summary>
    /// Amenity type options
    /// </summary>
    public List<EnumOptionDto> AmenityType { get; set; } = new();
}

/// <summary>
/// User permissions for various operations
/// </summary>
public class PermissionsDto
{
    /// <summary>
    /// Can create accommodation
    /// </summary>
    public bool CanCreateAccommodation { get; set; }

    /// <summary>
    /// Can edit accommodation
    /// </summary>
    public bool CanEditAccommodation { get; set; }

    /// <summary>
    /// Can delete accommodation
    /// </summary>
    public bool CanDeleteAccommodation { get; set; }

    /// <summary>
    /// Can create organisation
    /// </summary>
    public bool CanCreateOrganisation { get; set; }

    /// <summary>
    /// Can edit organisation
    /// </summary>
    public bool CanEditOrganisation { get; set; }

    /// <summary>
    /// Can delete organisation
    /// </summary>
    public bool CanDeleteOrganisation { get; set; }
}

/// <summary>
/// Represents an enum option with value and display name
/// </summary>
public class EnumOptionDto
{
    /// <summary>
    /// The enum value (numeric)
    /// </summary>
    public int Value { get; set; }

    /// <summary>
    /// The display name for the enum value
    /// </summary>
    public string Name { get; set; } = string.Empty;
}
