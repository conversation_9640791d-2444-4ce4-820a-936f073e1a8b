namespace EAMS.API.DTOs;

/// <summary>
/// Response wrapper for accommodations list with additional metadata
/// </summary>
public class AccommodationsResponseDto
{
    /// <summary>
    /// List of accommodations
    /// </summary>
    public IEnumerable<AccommodationDto> Accommodations { get; set; } = new List<AccommodationDto>();

    /// <summary>
    /// Indicates whether the current user can create new accommodations
    /// </summary>
    public bool CanCreateAccommodation { get; set; }
}
