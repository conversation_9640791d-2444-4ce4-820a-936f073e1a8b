namespace EAMS.API.DTOs;

/// <summary>
/// Response wrapper for organisations list with additional metadata
/// </summary>
public class OrganisationsResponseDto
{
    /// <summary>
    /// List of organisations
    /// </summary>
    public IEnumerable<OrganisationDto> Organisations { get; set; } = new List<OrganisationDto>();

    /// <summary>
    /// Indicates whether the current user can create new organisations
    /// </summary>
    public bool CanCreateOrganisation { get; set; }
}
